@article{NQ,
  title={Natural questions: a benchmark for question answering research},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and others},
  journal={Transactions of the Association for Computational Linguistics},
  volume={7},
  pages={453--466},
  year={2019},
}
@article{TrivialQA,
  title={Triviaqa: A large scale distantly supervised challenge dataset for reading comprehension},
  author={<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:1705.03551},
  year={2017}
}
@article{PopQA,
  title={When not to trust language models: Investigating effectiveness and limitations of parametric and non-parametric memories},
  author={<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2212.10511},
  volume={7},
  year={2022}
}
@article{HotpotQA,
  title={HotpotQA: A dataset for diverse, explainable multi-hop question answering},
  author={<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>gio, <PERSON>shua and <PERSON>, <PERSON> <PERSON> and <PERSON>akhutdinov, Ruslan and <PERSON>, <PERSON> D},
  journal={ar<PERSON>iv preprint ar<PERSON>iv:1809.09600},
  year={2018}
}
@article{2wiki,
  title={Constructing a multi-hop qa dataset for comprehensive evaluation of reasoning steps},
  author={Ho, Xanh and Nguyen, Anh-Khoa Duong and Sugawara, Saku and Aizawa, Akiko},
  journal={arXiv preprint arXiv:2011.01060},
  year={2020}
}
@article{MuSiQue,
  title={MuSiQue: Multihop Questions via Single-hop Question Composition},
  author={Trivedi, Harsh and Balasubramanian, Niranjan and Khot, Tushar and Sabharwal, Ashish},
  journal={Transactions of the Association for Computational Linguistics},
  volume={10},
  pages={539--554},
  year={2022},
}
@article{Bamboogle,
  title={Measuring and narrowing the compositionality gap in language models},
  author={Press, Ofir and Zhang, Muru and Min, Sewon and Schmidt, Ludwig and Smith, Noah A and Lewis, Mike},
  journal={arXiv preprint arXiv:2210.03350},
  year={2022}
}
@article{Search-r1,
  title={Search-r1: Training llms to reason and leverage search engines with reinforcement learning},
  author={Jin, Bowen and Zeng, Hansi and Yue, Zhenrui and Yoon, Jinsung and Arik, Sercan and Wang, Dong and Zamani, Hamed and Han, Jiawei},
  journal={arXiv preprint arXiv:2503.09516},
  year={2025}
}
@article{Search-r1-emperical,
  title={An Empirical Study on Reinforcement Learning for Reasoning-Search Interleaved LLM Agents},
  author={Jin, Bowen and Yoon, Jinsung and Kargupta, Priyanka and Arik, Sercan O and Han, Jiawei},
  journal={arXiv preprint arXiv:2505.15117},
  year={2025}
}
@article{COT,
  title={Chain-of-thought prompting elicits reasoning in large language models},
  author={Wei, Jason and Wang, Xuezhi and Schuurmans, Dale and Bosma, Maarten and Xia, Fei and Chi, Ed and Le, Quoc V and Zhou, Denny and others},
  journal={Advances in neural information processing systems},
  volume={35},
  pages={24824--24837},
  year={2022}
}
@article{RAG,
  title={Retrieval-augmented generation for knowledge-intensive nlp tasks},
  author={Lewis, Patrick and Perez, Ethan and Piktus, Aleksandra and Petroni, Fabio and Karpukhin, Vladimir and Goyal, Naman and K{\"u}ttler, Heinrich and Lewis, Mike and Yih, Wen-tau and Rockt{\"a}schel, Tim and others},
  journal={Advances in neural information processing systems},
  volume={33},
  pages={9459--9474},
  year={2020}
}
@article{IRCOT,
  title={Interleaving retrieval with chain-of-thought reasoning for knowledge-intensive multi-step questions},
  author={Trivedi, Harsh and Balasubramanian, Niranjan and Khot, Tushar and Sabharwal, Ashish},
  journal={arXiv preprint arXiv:2212.10509},
  year={2022}
}
@article{Search-o1,
  title={Search-o1: Agentic search-enhanced large reasoning models},
  author={Li, Xiaoxi and Dong, Guanting and Jin, Jiajie and Zhang, Yuyao and Zhou, Yujia and Zhu, Yutao and Zhang, Peitian and Dou, Zhicheng},
  journal={arXiv preprint arXiv:2501.05366},
  year={2025}
}
@article{SFT,
  title={Scaling instruction-finetuned language models},
  author={Chung, Hyung Won and Hou, Le and Longpre, Shayne and Zoph, Barret and Tay, Yi and Fedus, William and Li, Yunxuan and Wang, Xuezhi and Dehghani, Mostafa and Brahma, Siddhartha and others},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={70},
  pages={1--53},
  year={2024}
}
@article{Deepseek-r1,
  title={Deepseek-r1: Incentivizing reasoning capability in llms via reinforcement learning},
  author={Guo, Daya and Yang, Dejian and Zhang, Haowei and Song, Junxiao and Zhang, Ruoyu and Xu, Runxin and Zhu, Qihao and Ma, Shirong and Wang, Peiyi and Bi, Xiao and others},
  journal={arXiv preprint arXiv:2501.12948},
  year={2025}
}
@inproceedings{wiki,
  title={Dense Passage Retrieval for Open-Domain Question Answering.},
  author={Karpukhin, Vladimir and Oguz, Barlas and Min, Sewon and Lewis, Patrick SH and Wu, Ledell and Edunov, Sergey and Chen, Danqi and Yih, Wen-tau},
  booktitle={EMNLP (1)},
  pages={6769--6781},
  year={2020}
}
@article{E5,
  title={Text embeddings by weakly-supervised contrastive pre-training},
  author={Wang, Liang and Yang, Nan and Huang, Xiaolong and Jiao, Binxing and Yang, Linjun and Jiang, Daxin and Majumder, Rangan and Wei, Furu},
  journal={arXiv preprint arXiv:2212.03533},
  year={2022}
}
@article{DAPO,
  title={Dapo: An open-source llm reinforcement learning system at scale},
  author={Yu, Qiying and Zhang, Zheng and Zhu, Ruofei and Yuan, Yufeng and Zuo, Xiaochen and Yue, Yu and Dai, Weinan and Fan, Tiantian and Liu, Gaohong and Liu, Lingjun and others},
  journal={arXiv preprint arXiv:2503.14476},
  year={2025}
}
@article{uncertainty_rag,
  title={Uncertainty quantification in retrieval-augmented generation systems},
  author={Chen, Wei and Liu, Xiaoming and Zhang, Yifan and Wang, Jianhua},
  journal={Proceedings of the International Conference on Machine Learning},
  volume={40},
  pages={2156--2167},
  year={2024}
}

@article{LogTokU,
  title={Estimating LLM Uncertainty with Evidence},
  author={Ma, Huan and Chen, Jingdong and Joey Tianyi Zhou and Wang, Guangyu and Zhang, Changqing},
  journal={arXiv preprint arXiv:2502.00290},
  year={2025}
}

@article{DeepSeekMath,
  title={Deepseekmath: Pushing the limits of mathematical reasoning in open language models},
  author={Shao, Zhihong and Wang, Peiyi and Zhu, Qihao and Xu, Runxin and Song, Junxiao and Bi, Xiao and Zhang, Haowei and Zhang, Mingchuan and Li, YK and Wu, Yang and others},
  journal={arXiv preprint arXiv:2402.03300},
  year={2024}
}

@article{ReSearch,
  title={Learning to reason with search for llms via reinforcement learning},
  author={Chen, Mingyang and Li, Tianpeng and Sun, Haoze and Zhou, Yijie and Zhu, Chenzheng and Wang, Haofen and Pan, Jeff Z and Zhang, Wen and Chen, Huajun and Yang, Fan and others},
  journal={arXiv preprint arXiv:2503.19470},
  year={2025}
}

@article{EmpiricalStudy,
  title={An Empirical Study on Reinforcement Learning for Reasoning-Search Interleaved LLM Agents},
  author={Jin, Bowen and Yoon, Jinsung and Kargupta, Priyanka and Arik, Sercan O and Han, Jiawei},
  journal={arXiv preprint arXiv:2505.15117},
  year={2025}
}

@article{yue2025does,
  title={Does reinforcement learning really incentivize reasoning capacity in llms beyond the base model?},
  author={Yue, Yang and Chen, Zhiqi and Lu, Rui and Zhao, Andrew and Wang, Zhaokai and Song, Shiji and Huang, Gao},
  journal={arXiv preprint arXiv:2504.13837},
  year={2025}
}

@article{wen2025reinforcement,
  title={Reinforcement Learning with Verifiable Rewards Implicitly Incentivizes Correct Reasoning in Base LLMs},
  author={Wen, Xumeng and Liu, Zihan and Zheng, Shun and Xu, Zhijian and Ye, Shengyu and Wu, Zhirong and Liang, Xiao and Wang, Yang and Li, Junjie and Miao, Ziming and others},
  journal={arXiv preprint arXiv:2506.14245},
  year={2025}
}

@article{GiGPO,
  title={Group-in-group policy optimization for llm agent training},
  author={Feng, Lang and Xue, Zhenghai and Liu, Tingcong and An, Bo},
  journal={arXiv preprint arXiv:2505.10978},
  year={2025}
}

@article{RAG-Gym,
  title={Rag-gym: Optimizing reasoning and search agents with process supervision},
  author={Xiong, Guangzhi and Jin, Qiao and Wang, Xiao and Fang, Yin and Liu, Haolin and Yang, Yifan and Chen, Fangyuan and Song, Zhixing and Wang, Dengyu and Zhang, Minjia and others},
  journal={arXiv preprint arXiv:2502.13957},
  year={2025}
}

@article{MA-RAG,
  title={MA-RAG: Multi-Agent Retrieval-Augmented Generation via Collaborative Chain-of-Thought Reasoning},
  author={Nguyen, Thang and Chin, Peter and Tai, Yu-Wing},
  journal={arXiv preprint arXiv:2505.20096},
  year={2025}
}

@article{LUFFY,
  title={Learning to reason under off-policy guidance},
  author={Yan, Jianhao and Li, Yafu and Hu, Zican and Wang, Zhi and Cui, Ganqu and Qu, Xiaoye and Cheng, Yu and Zhang, Yue},
  journal={arXiv preprint arXiv:2504.14945},
  year={2025}
}

@article{ULTRA,
  title={LLM-Guided Reinforcement Learning: Addressing Training Bottlenecks through Policy Modulation},
  author={Tan, Heng and Yan, Hua and Yang, Yu},
  journal={arXiv preprint arXiv:2505.20671},
  year={2025}
}

@article{ReasoningEra,
  title={Towards reasoning era: A survey of long chain-of-thought for reasoning large language models},
  author={Chen, Qiguang and Qin, Libo and Liu, Jinhao and Peng, Dengyun and Guan, Jiannan and Wang, Peng and Hu, Mengkang and Zhou, Yuhang and Gao, Te and Che, Wanxiang},
  journal={arXiv preprint arXiv:2503.09567},
  year={2025}
}

@article{liu2025understanding,
  title={Understanding r1-zero-like training: A critical perspective},
  author={Liu, Zichen and Chen, Changyu and Li, Wenjun and Qi, Penghui and Pang, Tianyu and Du, Chao and Lee, Wee Sun and Lin, Min},
  journal={arXiv preprint arXiv:2503.20783},
  year={2025}
}

@article{colmerauer1990introduction,
  title={An introduction to Prolog III},
  author={Colmerauer, Alain},
  journal={Communications of the ACM},
  volume={33},
  number={7},
  pages={69--90},
  year={1990},
  publisher={ACM New York, NY, USA}
}

@article{Self-Reflection,
  title={Self-rag: Learning to retrieve, generate, and critique through self-reflection},
  author={Asai, Akari and Wu, Zeqiu and Wang, Yizhong and Sil, Avirup and Hajishirzi, Hannaneh},
  year={2024},
  publisher={ICLR}
}

@article{gao2025synergizing,
  title={Synergizing rag and reasoning: A systematic review},
  author={Gao, Yunfan and Xiong, Yun and Zhong, Yijie and Bi, Yuxi and Xue, Ming and Wang, Haofen},
  journal={arXiv preprint arXiv:2504.15909},
  year={2025}
}

@article{arslan2024survey,
  title={A Survey on RAG with LLMs},
  author={Arslan, Muhammad and Ghanem, Hussam and Munawar, Saba and Cruz, Christophe},
  journal={Procedia computer science},
  volume={246},
  pages={3781--3790},
  year={2024},
  publisher={Elsevier}
}

@article{R1-Searcher,
  title={R1-searcher: Incentivizing the search capability in llms via reinforcement learning},
  author={Song, Huatong and Jiang, Jinhao and Min, Yingqian and Chen, Jie and Chen, Zhipeng and Zhao, Wayne Xin and Fang, Lei and Wen, Ji-Rong},
  journal={arXiv preprint arXiv:2503.05592},
  year={2025}
}

@article{R1-Searcher++,
  title={R1-Searcher++: Incentivizing the Dynamic Knowledge Acquisition of LLMs via Reinforcement Learning},
  author={Song, Huatong and Jiang, Jinhao and Tian, Wenqing and Chen, Zhipeng and Wu, Yuhuan and Zhao, Jiahao and Min, Yingqian and Zhao, Wayne Xin and Fang, Lei and Wen, Ji-Rong},
  journal={arXiv preprint arXiv:2505.17005},
  year={2025}
}

@article{Deepresearcher,
  title={Deepresearcher: Scaling deep research via reinforcement learning in real-world environments},
  author={Zheng, Yuxiang and Fu, Dayuan and Hu, Xiangkun and Cai, Xiaojie and Ye, Lyumanshan and Lu, Pengrui and Liu, Pengfei},
  journal={arXiv preprint arXiv:2504.03160},
  year={2025}
}

@article{RLHF,
  title={Training language models to follow instructions with human feedback},
  author={Ouyang, Long and Wu, Jeffrey and Jiang, Xu and Almeida, Diogo and Wainwright, Carroll and Mishkin, Pamela and Zhang, Chong and Agarwal, Sandhini and Slama, Katarina and Ray, Alex and others},
  journal={Advances in neural information processing systems},
  volume={35},
  pages={27730--27744},
  year={2022}
}

@article{DPO,
  title={Direct preference optimization: Your language model is secretly a reward model},
  author={Rafailov, Rafael and Sharma, Archit and Mitchell, Eric and Manning, Christopher D and Ermon, Stefano and Finn, Chelsea},
  journal={Advances in neural information processing systems},
  volume={36},
  pages={53728--53741},
  year={2023}
}

@article{Zerosearch,
  title={Zerosearch: Incentivize the search capability of llms without searching},
  author={Sun, Hao and Qiao, Zile and Guo, Jiayan and Fan, Xuanbo and Hou, Yingyan and Jiang, Yong and Xie, Pengjun and Zhang, Yan and Huang, Fei and Zhou, Jingren},
  journal={arXiv preprint arXiv:2505.04588},
  year={2025}
}

@article{MMSearch-R1,
  title={MMSearch-R1: Incentivizing LMMs to Search},
  author={Wu, Jinming and Deng, Zihao and Li, Wei and Liu, Yiding and You, Bo and Li, Bo and Ma, Zejun and Liu, Ziwei},
  journal={arXiv preprint arXiv:2506.20670},
  year={2025}
}

@article{mei2025O2,
  title={O $\^{} 2$-Searcher: A Searching-based Agent Model for Open-Domain Open-Ended Question Answering},
  author={Mei, Jianbiao and Hu, Tao and Fu, Daocheng and Wen, Licheng and Yang, Xuemeng and Wu, Rong and Cai, Pinlong and Cai, Xinyu and Gao, Xing and Yang, Yu and others},
  journal={arXiv preprint arXiv:2505.16582},
  year={2025}
}

@article{qian2025scent,
  title={Scent of Knowledge: Optimizing Search-Enhanced Reasoning with Information Foraging},
  author={Qian, Hongjin and Liu, Zheng},
  journal={arXiv preprint arXiv:2505.09316},
  year={2025}
}

@article{R3-RAG,
  title={R3-RAG: Learning Step-by-Step Reasoning and Retrieval for LLMs via Reinforcement Learning},
  author={Li, Yuan and Luo, Qi and Li, Xiaonan and Li, Bufan and Cheng, Qinyuan and Wang, Bo and Zheng, Yining and Wang, Yuxin and Yin, Zhangyue and Qiu, Xipeng},
  journal={arXiv preprint arXiv:2505.23794},
  year={2025}
}

@article{zhang2025100,
  title={100 days after deepseek-r1: A survey on replication studies and more directions for reasoning language models},
  author={Zhang, Chong and Deng, Yue and Lin, Xiang and Wang, Bin and Ng, Dianwen and Ye, Hai and Li, Xingxuan and Xiao, Yao and Mo, Zhanfeng and Zhang, Qi and others},
  journal={arXiv preprint arXiv:2505.00551},
  year={2025}
}

@article{li2025towards,
  title={Towards Agentic RAG with Deep Reasoning: A Survey of RAG-Reasoning Systems in LLMs},
  author={Li, Yangning and Zhang, Weizhi and Yang, Yuyao and Huang, Wei-Chieh and Wu, Yaozu and Luo, Junyu and Bei, Yuanchen and Zou, Henry Peng and Luo, Xiao and Zhao, Yusheng and others},
  journal={arXiv preprint arXiv:2507.09477},
  year={2025}
}