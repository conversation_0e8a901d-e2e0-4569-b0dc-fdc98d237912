\section{Conclusion}

This work addresses the dead end problem in reinforcement learning-based retrieval-augmented generation, where models become trapped in unproductive reasoning paths during policy optimization. Our REX-RAG framework introduces the mixed sampling strategy and the policy realignment mechanism to enable systematic exploration while maintaining training stability. Comprehensive experiments demonstrate consistent improvements over strong baselines, with particularly notable gains on multi-hop reasoning tasks.
Our key contribution lies in providing a principled approach to exploration in LLM reasoning systems through importance sampling-based distributional correction. 
% The framework reveals that effective exploration occurs when models exhibit high aleatoric uncertainty combined with low epistemic uncertainty, suggesting they possess relevant knowledge but face multiple valid reasoning paths. 
This insight may offers a practical solution for improving retrieval-augmented generation systems and provides a new exploration perspective for LLM reinforcement learning.


