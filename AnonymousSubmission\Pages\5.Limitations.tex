\section{Limitations}

While REX-RAG demonstrates significant improvements over existing methods, several limitations warrant careful consideration for future research and practical deployment.

\paragraph{Limited Exploration Strategy}
Our current exploration mechanism relies on a relatively simple strategy of inserting prompts from a pre-constructed prompt pool to guide reasoning exploration. While this approach proves effective, more sophisticated exploration strategies could potentially yield better results. For instance, from a prompt perspective, online model-generated prompts could provide more adaptive and context-specific guidance compared to our fixed prompt pool. From a policy perspective, leveraging backtracking tree structures or more complex search algorithms could enable more systematic exploration of the reasoning space. Additionally, our approach primarily focuses on local exploration through prompt insertion rather than global reasoning path restructuring. Despite these limitations, our work demonstrates that end-to-end optimization under an exploratory policy is feasible and effective, establishing a foundation for more advanced exploration strategies in future work.

\paragraph{Computational Overhead and Adaptive Sampling Limitations}
The mixed sampling strategy inherently introduces computational overhead compared to standard policy optimization approaches. Our resampling mechanism requires a two-stage process: first performing normal sampling to assess question difficulty through initial trajectory evaluation, then conducting exploratory sampling based on the observed failure rates. This sequential approach increases computational complexity as it necessitates generating $(1-\alpha)G$ additional exploratory trajectories from the probe policy $\pi_\varepsilon$, resulting in approximately 12\% more trajectory sampling in our experiments. While this overhead is substantially more efficient than uniform oversampling approaches (which require 20\% additional trajectories for minimal gains), the computational cost scales linearly with the resampling parameter $p$ and the exploration ratio $\alpha$. A more efficient approach would involve predicting question difficulty a priori and automatically adjusting sampling quantities accordingly, eliminating the need for the initial sampling phase. However, developing reliable difficulty prediction mechanisms remains an open challenge. Furthermore, the policy realignment mechanism requires computing importance sampling ratios for each token, adding non-negligible computational complexity during training.

\paragraph{Theoretical Assumptions and Distributional Constraints}
Our approach relies on several theoretical assumptions that may not hold universally. The importance sampling-based distributional correction assumes that the probe policy $\pi_\varepsilon$ does not deviate significantly from the target policy $\pi_\theta$, particularly for inserted prompt tokens. When this assumption is violated—such as when inserted tokens exhibit extremely low probabilities under $\pi_\theta$—the importance sampling ratios become negligibly small, leading to ineffective gradient updates despite positive advantages. Additionally, the theoretical guarantees of unbiased policy gradient estimation depend on accurate likelihood estimation of probe-induced trajectories, which may be challenging to maintain across diverse reasoning scenarios and domain shifts.



\paragraph{Prompt Pool Dependency and Domain Specificity}
The effectiveness of REX-RAG critically depends on the quality and diversity of the curated prompt pool used for exploration. Our current approach employs a fixed collection of chain-of-thought prompts, which may not generalize optimally across all reasoning domains or question types. The prompt pool requires careful curation and may need domain-specific adaptation for optimal performance. While our experiments show robustness to specific linguistic forms of prompts, the fundamental limitation remains that the exploration space is bounded by the diversity and relevance of the prompt collection. This dependency could limit the framework's effectiveness in novel domains or reasoning patterns not anticipated during prompt pool construction.

\paragraph{Evaluation and Dataset Limitations}
Our experimental evaluation, while comprehensive across seven benchmarks, is primarily focused on question-answering tasks with exact match evaluation criteria. The framework's effectiveness on other types of reasoning tasks, such as mathematical problem solving, code generation, or creative reasoning, remains to be thoroughly validated. Additionally, our evaluation relies heavily on exact match metrics, which may not capture the full spectrum of reasoning quality improvements. The training data combination (merging NQ and HotpotQA) may introduce domain-specific biases that could affect generalization to truly out-of-domain scenarios beyond the tested benchmarks.

\paragraph{Dead End Detection and Recovery Mechanisms}
While REX-RAG addresses the dead end problem through mixed sampling, the current approach relies on binary reward signals (correct/incorrect answers) to identify dead ends. This binary detection mechanism may miss more nuanced forms of reasoning failures or suboptimal paths that do not immediately lead to incorrect answers. The framework lacks sophisticated mechanisms for detecting partial reasoning errors or identifying when the model is pursuing suboptimal but not entirely incorrect reasoning chains. More fine-grained dead end detection could potentially improve exploration efficiency and reasoning quality.

\paragraph{Limited Analysis of Failure Cases}
Our current analysis provides limited insight into specific scenarios where REX-RAG fails to improve performance or where the exploration mechanism may be counterproductive. The framework may struggle with questions that require highly specialized domain knowledge not well-represented in the retrieval corpus, or with reasoning tasks that benefit more from systematic logical deduction rather than exploratory search. Understanding these failure modes more comprehensively would inform better design choices and help practitioners identify appropriate use cases.

\paragraph{Hyperparameter Sensitivity and Tuning Complexity}
REX-RAG introduces several hyperparameters ($\alpha$, $p$, trajectory filtering thresholds) that require careful tuning for optimal performance. While we provide default values based on our experiments, the sensitivity of these parameters across different domains, model sizes, and reasoning tasks remains underexplored. The interaction between these hyperparameters and their optimal values may vary significantly across different deployment scenarios, potentially requiring extensive hyperparameter search for new applications.

\paragraph{Future Research Directions}
Several promising directions could address these limitations: (1) developing adaptive computational allocation strategies that dynamically adjust exploration intensity based on reasoning complexity; (2) investigating more sophisticated dead end detection mechanisms using intermediate reasoning quality assessment; (3) exploring automatic prompt pool generation and adaptation techniques; (4) extending evaluation to broader reasoning tasks beyond question-answering; (5) developing theoretical frameworks for optimal exploration-exploitation trade-offs in reasoning systems; and (6) investigating the framework's effectiveness with different base model architectures and sizes.

Despite these limitations, REX-RAG provides a principled foundation for exploration in retrieval-augmented reasoning systems, and addressing these constraints represents important avenues for advancing the field of reasoning-enhanced language models.
