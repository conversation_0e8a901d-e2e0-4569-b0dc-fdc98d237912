\begin{thebibliography}{15}
\providecommand{\natexlab}[1]{#1}

\bibitem[{<PERSON> et~al.(2024)<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> et~al.}]{SFT}
<PERSON>, <PERSON>.~<PERSON>.; <PERSON>, L.; <PERSON>, S.; <PERSON>, B.; <PERSON>, Y.; <PERSON>, W.; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, M.; Brahma, S.; et~al. 2024.
\newblock Scaling instruction-finetuned language models.
\newblock \emph{Journal of Machine Learning Research}, 25(70): 1--53.

\bibitem[{<PERSON> et~al.(2025)<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> et~al.}]{Deepseek-r1}
<PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>.; <PERSON>, S.; <PERSON>, <PERSON>.; <PERSON>i, X.; et~al. 2025.
\newblock Deepseek-r1: Incentivizing reasoning capability in llms via reinforcement learning.
\newblock \emph{arXiv preprint arXiv:2501.12948}.

\bibitem[{Ho et~al.(2020)<PERSON>, <PERSON>, <PERSON>, and Ai<PERSON>}]{2wi<PERSON>}
<PERSON>, X.; <PERSON>uyen, A.-K.~D.; Sugawara, S.; and Aizawa, A. 2020.
\newblock Constructing a multi-hop qa dataset for comprehensive evaluation of reasoning steps.
\newblock \emph{arXiv preprint arXiv:2011.01060}.

\bibitem[{Jin et~al.(2025{\natexlab{a}})Jin, Yoon, Kargupta, Arik, and Han}]{Search-r1-emperical}
Jin, B.; Yoon, J.; Kargupta, P.; Arik, S.~O.; and Han, J. 2025{\natexlab{a}}.
\newblock An Empirical Study on Reinforcement Learning for Reasoning-Search Interleaved LLM Agents.
\newblock \emph{arXiv preprint arXiv:2505.15117}.

\bibitem[{Jin et~al.(2025{\natexlab{b}})Jin, Zeng, Yue, Yoon, Arik, Wang, Zamani, and Han}]{Search-r1}
Jin, B.; Zeng, H.; Yue, Z.; Yoon, J.; Arik, S.; Wang, D.; Zamani, H.; and Han, J. 2025{\natexlab{b}}.
\newblock Search-r1: Training llms to reason and leverage search engines with reinforcement learning.
\newblock \emph{arXiv preprint arXiv:2503.09516}.

\bibitem[{Joshi et~al.(2017)Joshi, Choi, Weld, and Zettlemoyer}]{TrivialQA}
Joshi, M.; Choi, E.; Weld, D.~S.; and Zettlemoyer, L. 2017.
\newblock Triviaqa: A large scale distantly supervised challenge dataset for reading comprehension.
\newblock \emph{arXiv preprint arXiv:1705.03551}.

\bibitem[{Kwiatkowski et~al.(2019)Kwiatkowski, Palomaki, Redfield, Collins, Parikh, Alberti, Epstein, Polosukhin, Devlin, Lee et~al.}]{NQ}
Kwiatkowski, T.; Palomaki, J.; Redfield, O.; Collins, M.; Parikh, A.; Alberti, C.; Epstein, D.; Polosukhin, I.; Devlin, J.; Lee, K.; et~al. 2019.
\newblock Natural questions: a benchmark for question answering research.
\newblock \emph{Transactions of the Association for Computational Linguistics}, 7: 453--466.

\bibitem[{Lewis et~al.(2020)Lewis, Perez, Piktus, Petroni, Karpukhin, Goyal, K{\"u}ttler, Lewis, Yih, Rockt{\"a}schel et~al.}]{RAG}
Lewis, P.; Perez, E.; Piktus, A.; Petroni, F.; Karpukhin, V.; Goyal, N.; K{\"u}ttler, H.; Lewis, M.; Yih, W.-t.; Rockt{\"a}schel, T.; et~al. 2020.
\newblock Retrieval-augmented generation for knowledge-intensive nlp tasks.
\newblock \emph{Advances in neural information processing systems}, 33: 9459--9474.

\bibitem[{Li et~al.(2025)Li, Dong, Jin, Zhang, Zhou, Zhu, Zhang, and Dou}]{Search-o1}
Li, X.; Dong, G.; Jin, J.; Zhang, Y.; Zhou, Y.; Zhu, Y.; Zhang, P.; and Dou, Z. 2025.
\newblock Search-o1: Agentic search-enhanced large reasoning models.
\newblock \emph{arXiv preprint arXiv:2501.05366}.

\bibitem[{Ma et~al.(2025)Ma, Chen, Zhou, Wang, and Zhang}]{LogTokU}
Ma, H.; Chen, J.; Zhou, J.~T.; Wang, G.; and Zhang, C. 2025.
\newblock Estimating LLM Uncertainty with Evidence.
\newblock \emph{arXiv preprint arXiv:2502.00290}.

\bibitem[{Mallen et~al.(2022)Mallen, Asai, Zhong, Das, Hajishirzi, and Khashabi}]{PopQA}
Mallen, A.; Asai, A.; Zhong, V.; Das, R.; Hajishirzi, H.; and Khashabi, D. 2022.
\newblock When not to trust language models: Investigating effectiveness and limitations of parametric and non-parametric memories.
\newblock \emph{arXiv preprint arXiv:2212.10511}, 7.

\bibitem[{Press et~al.(2022)Press, Zhang, Min, Schmidt, Smith, and Lewis}]{Bamboogle}
Press, O.; Zhang, M.; Min, S.; Schmidt, L.; Smith, N.~A.; and Lewis, M. 2022.
\newblock Measuring and narrowing the compositionality gap in language models.
\newblock \emph{arXiv preprint arXiv:2210.03350}.

\bibitem[{Trivedi et~al.(2022{\natexlab{a}})Trivedi, Balasubramanian, Khot, and Sabharwal}]{IRCOT}
Trivedi, H.; Balasubramanian, N.; Khot, T.; and Sabharwal, A. 2022{\natexlab{a}}.
\newblock Interleaving retrieval with chain-of-thought reasoning for knowledge-intensive multi-step questions.
\newblock \emph{arXiv preprint arXiv:2212.10509}.

\bibitem[{Trivedi et~al.(2022{\natexlab{b}})Trivedi, Balasubramanian, Khot, and Sabharwal}]{MuSiQue}
Trivedi, H.; Balasubramanian, N.; Khot, T.; and Sabharwal, A. 2022{\natexlab{b}}.
\newblock MuSiQue: Multihop Questions via Single-hop Question Composition.
\newblock \emph{Transactions of the Association for Computational Linguistics}, 10: 539--554.

\bibitem[{Yang et~al.(2018)Yang, Qi, Zhang, Bengio, Cohen, Salakhutdinov, and Manning}]{HotpotQA}
Yang, Z.; Qi, P.; Zhang, S.; Bengio, Y.; Cohen, W.~W.; Salakhutdinov, R.; and Manning, C.~D. 2018.
\newblock HotpotQA: A dataset for diverse, explainable multi-hop question answering.
\newblock \emph{arXiv preprint arXiv:1809.09600}.

\end{thebibliography}
