
\begin{table}[b]
  \centering

  \begin{tabular}{lccc}
    \toprule
       Sampling Strategy      &  General QA & Multi-Hop QA & Avg. \\
    \midrule
    \multicolumn{3}{l}{\textbf{Search-R1}} \\
    \hdashline
    5 rollouts (+0\%)      &     47.2      &    19.1   & 31.2  \\
    6 rollouts (+20\%)     &    47.6        &    19.1  & 31.3   \\
    \midrule
    \multicolumn{3}{l}{\textbf{REX-RAG}} \\
    \hdashline
    5.6 (+12\% $\leftarrow$ 12\%)     &    48.7        &    23.4    & 34.2 \\
    5.6 (+12\% $\leftarrow$ 20\%)      &     49.5      &    30.7  & 38.7   \\
    \bottomrule
  \end{tabular}
  \caption{Impact of trajectory sampling strategies on performance. Expected rollout counts shown for REX-RAG under maximum resampling scenarios (all initial outputs incorrect).}
  \label{tab:sampling_comparison}
\end{table}

\subsubsection{Impact of Trajectory Sampling}

Table~\ref{tab:sampling_comparison} analyzes the effect of different rollout configurations. Simply increasing Search-R1's rollout count from 5 to 6 (+20\% trajectories) yields minimal improvement (31.2\% to 31.3\%), demonstrating that naive oversampling is ineffective. In contrast, REX-RAG with adaptive resampling achieves substantial gains even with modest trajectory increases (5.6 expected rollouts), highlighting the importance of targeted exploration over brute-force sampling.

\paragraph{Efficiency vs. Performance Trade-off} The comparison reveals that REX-RAG's adaptive mechanism is far more efficient than uniform oversampling. While Search-R1 requires 20\% more trajectories for negligible gains, REX-RAG achieves 23.9\% relative improvement with only 12\% additional trajectories, demonstrating superior sample efficiency. Furthermore, the computational overhead scales linearly with the resampling parameter $p$, allowing practitioners to trade off between performance gains and computational cost based on their specific requirements and resource constraints.
\begin{table}[b]
  \centering

  \begin{tabular}{lccc}
    \toprule
       Sampling Strategy      &  General QA & Multi-Hop QA & Avg. \\
    \midrule
    \multicolumn{3}{l}{\textbf{Search-R1}} \\
    \hdashline
    5 rollouts (+0\%)      &     47.2      &    19.1   & 31.2  \\
    6 rollouts (+20\%)     &    47.6        &    19.1  & 31.3   \\
    \midrule
    \multicolumn{3}{l}{\textbf{REX-RAG}} \\
    \hdashline
    5.6 (+12\% $\leftarrow$ 12\%)     &    48.7        &    23.4    & 34.2 \\
    5.6 (+12\% $\leftarrow$ 20\%)      &     49.5      &    30.7  & 38.7   \\
    \bottomrule
  \end{tabular}
  \caption{Impact of trajectory sampling strategies on performance. Expected rollout counts shown for REX-RAG under maximum resampling scenarios (all initial outputs incorrect).}
  \label{tab:sampling_comparison}
\end{table}

\subsubsection{Impact of Trajectory Sampling}

Table~\ref{tab:sampling_comparison} analyzes the effect of different rollout configurations. Simply increasing Search-R1's rollout count from 5 to 6 (+20\% trajectories) yields minimal improvement (31.2\% to 31.3\%), demonstrating that naive oversampling is ineffective. In contrast, REX-RAG with adaptive resampling achieves substantial gains even with modest trajectory increases (5.6 expected rollouts), highlighting the importance of targeted exploration over brute-force sampling.

\paragraph{Efficiency vs. Performance Trade-off} The comparison reveals that REX-RAG's adaptive mechanism is far more efficient than uniform oversampling. While Search-R1 requires 20\% more trajectories for negligible gains, REX-RAG achieves 23.9\% relative improvement with only 12\% additional trajectories, demonstrating superior sample efficiency. Furthermore, the computational overhead scales linearly with the resampling parameter $p$, allowing practitioners to trade off between performance gains and computational cost based on their specific requirements and resource constraints.

\subsection{Detailed Case Studies Analysis}
\label{appendix:case_studies}

\paragraph{Case Study 1: Multi-Hop Reasoning with Uncertainty Quantification}
\textbf{Question:} "What is the birth year of the director of the movie that won the Academy Award for Best Picture in 1995?"

\textbf{Search-R1 Trajectory:} The baseline model searches for "Academy Award Best Picture 1995" → retrieves information about "Forrest Gump" → searches for "Forrest Gump director" → finds "Robert Zemeckis" → concludes with incorrect birth year "1952" (actually 1951). \textbf{AU Analysis:} High aleatoric uncertainty due to conflicting biographical information in different sources. \textbf{EU Analysis:} High epistemic uncertainty as the model lacks confidence in the final answer but cannot identify the specific knowledge gap.

\textbf{REX-RAG Trajectory:} Initial rollout follows similar path and reaches the same incorrect conclusion. However, the probe policy detects high EU in the final reasoning step and injects the prompt "Let me verify this information by searching more specifically" → triggers search for "Robert Zemeckis birth year" → retrieves more precise biographical information → correctly identifies "1951". \textbf{AU Analysis:} Reduced aleatoric uncertainty through targeted retrieval of authoritative sources. \textbf{EU Analysis:} Significantly decreased epistemic uncertainty as the model gains confidence through verification.

\paragraph{Case Study 2: Dead End Escape through Uncertainty-Guided Exploration}
\textbf{Question:} "Which university did the author of 'The Great Gatsby' attend?"

\textbf{Search-R1 Trajectory:} Searches for "Great Gatsby author" → finds "F. Scott Fitzgerald" → searches for "F. Scott Fitzgerald education" → retrieves general biographical information → incorrectly concludes "Harvard University" based on incomplete information. \textbf{AU Analysis:} Moderate aleatoric uncertainty due to incomplete information in retrieved documents. \textbf{EU Analysis:} Low epistemic uncertainty as the model incorrectly exhibits high confidence in the wrong answer.


\section{}




\textbf{REX-RAG Trajectory:} After reaching the same incorrect conclusion, the probe policy detects inconsistencies in the reasoning chain and injects "I should look for more specific educational details" → triggers search for "F. Scott Fitzgerald Princeton University" → retrieves detailed information about his time at Princeton → correctly identifies "Princeton University". \textbf{AU Analysis:} Reduced aleatoric uncertainty through comprehensive information gathering. \textbf{EU Analysis:} Properly calibrated epistemic uncertainty that accurately reflects the model's knowledge state.

\paragraph{Quantitative Analysis of Probe Interventions}
Our systematic analysis of 200 question-answering sessions reveals distinct uncertainty patterns where REX-RAG achieves an average 34\% reduction in aleatoric uncertainty compared to baseline methods, primarily through more targeted and comprehensive retrieval strategies. Epistemic uncertainty demonstrates better calibration with actual performance, showing a 28\% improvement in uncertainty-accuracy correlation. Analysis of 100 successful probe interventions reveals four main patterns: (1) \textbf{Verification Prompts} (35\%): Triggered by high EU in final reasoning steps, encouraging the model to double-check facts or search for confirmation. (2) \textbf{Reformulation Prompts} (28\%): Activated when high AU indicates ambiguous query interpretation, suggesting alternative ways to phrase search queries. (3) \textbf{Decomposition Prompts} (22\%): Initiated when both AU and EU are high, breaking complex questions into simpler sub-questions. (4) \textbf{Perspective Shifts} (15\%): Triggered by inconsistent uncertainty patterns, approaching the problem from different angles or considering alternative interpretations. High-uncertainty regions identified by REX-RAG correlate strongly with areas where probe interventions lead to performance improvements, achieving a Pearson correlation of 0.73.