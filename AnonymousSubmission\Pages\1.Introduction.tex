\section{Introduction}



Recent advances have shown that reinforcement learning (RL) offers a promising avenue for training large language models (LLMs) to perform complex reasoning tasks~\cite{RLHF, DAPO, ReasoningEra}. By integrating multi-step reasoning with retrieval-augmented generation (RAG), RL-trained LLMs can dynamically leverage external knowledge sources—essentially allowing them to "think while searching"~\cite{ReSearch, Search-r1}. This paradigm holds particular promise for multi-hop question answering, where models must iteratively gather and synthesize evidence across multiple queries to arrive at well-founded conclusions~\cite{EmpiricalStudy}.

\begin{figure}[t]
  \centering
  \includegraphics[width=0.45\textwidth]{figures/motivation.pdf}
  \caption{Framework comparison between existing approaches and REX-RAG. (a) Self-reflection: when encountering incorrect answers, the model attempts to ``rethink", but often produces similar trajectories that lead to dead ends with no effective updates. (b) REX-RAG: our method employs mixed sampling with exploration trajectories guided by diverse reasoning prompts, followed by policy realignment to ensure low-bias policy updates.}
  \label{fig:framework_comparison}
\end{figure}

Despite this potential, we observe a critical challenge that substantially hinders policy optimization in such settings. During RL training, LLMs frequently become trapped in what we term \textit{dead ends}: situations where the model consistently fails to arrive at the correct final answer after multiple rollouts. This phenomenon often stems from premature or overconfident conclusions drawn despite insufficient supporting information, effectively terminating exploration along potentially fruitful reasoning ~\cite{yue2025does, wen2025reinforcement, liu2025understanding}. 

% In our experiments with the Qwen2.5-3B-Base model, we found that approximately 60\% of training instances resulted in such dead ends, significantly limiting the model's ability to learn effective reasoning strategies.

Addressing this challenge requires mechanisms that can proactively explore alternative reasoning paths when initial trajectories prove unproductive. A straightforward solution is \textit{self-reflection}~\cite{Deepseek-r1, Search-r1}, which attempts to revise failed reasoning chains to generate alternative ones. However, we observe that these revised trajectories are often merely slight perturbations of the original paths, offering limited novelty and insufficient deviation to meaningfully explore alternative solutions. Consequently, it struggles to escapee from dead-end reasoning paths, as illustrated in Figure~\ref{fig:framework_comparison}(a). In our experiments with the Qwen2.5-3B model, self-reflection consistently results in a high incidence of "dead ends," where LLMs generate wrong answers across all rollouts. This phenomenon surpasses 85\% in the early phases of RL training and significantly impedes effective policy learning, as shown in Figure~\ref{fig:training_dynamics}.

% This occurs because self-reflection typically operates by asking the model to "think again," which tends to produce variations that remain within the same local reasoning space, failing to drive the reasoning process out of dead ends. 

On the other hand, more aggressively enforcing exploration, such as introducing additional agents~\cite{RAG-Gym, MA-RAG}, makes end-to-end optimization challenging due to the complexity of jointly training multiple components. This tension underscores the need for principled strategies that can foster sufficiently diverse and informative exploration while ensuring stable and unbiased policy optimization without compromising the end-to-end learning paradigm~\cite{GiGPO}.

To address this challenge, we propose \textbf{REX-RAG} (\textbf{R}easoning \textbf{EX}ploration with Policy Realignment in Retrieval-Augmented Generation), a novel framework that systematically explores alternative reasoning paths while maintaining rigorous policy learning through principled distributional corrections. Our approach introduces a more exploratory probe distribution $\pi_\varepsilon$ that works alongside the standard policy $\pi_\theta$ to guide exploration away from dead ends through mixed sampling strategies, as shown in Figure~\ref{fig:framework_comparison}(b).

The key innovation of REX-RAG lies in its \textit{Mixed Sampling Strategy} that combines exploration and exploitation in a principled manner. Our framework employs a curated collection of chain-of-thought prompts to inject diverse reasoning directions when trajectories fail. Specifically, when the policy encounters a dead end—indicated by incorrect answers—we strategically insert concise reasoning hints from the prompt pool and resume generation from that point, effectively steering the model toward unexplored solution paths. This approach generates substantially different reasoning trajectories that can escape local optima while maintaining computational efficiency.

\begin{figure}[t]
  \centering
  \includegraphics[width=0.4\textwidth]{figures/motivation2.pdf}
  \caption{Training dynamics comparison between self-reflection and REX-RAG. Top: Success rate over training steps, showing REX-RAG (red) achieving higher and more stable performance compared to self-reflection (blue). Bottom: Dead end rate over training steps, demonstrating that REX-RAG effectively reduces dead ends throughout while self-reflection shows persistent high "dead end" rates.}
  \label{fig:training_dynamics}
\end{figure}

Crucially, to prevent the distributional shifts inherent in such interventions from destabilizing training, REX-RAG incorporates a \textit{Policy Realignment Mechanism} based on importance sampling theory. This mechanism accurately estimates the likelihood of probe-induced trajectories and applies appropriate corrections to minimize the bias in the policy gradient, under mixed sampling from both the original policy and the probe distribution~\cite{LUFFY, ULTRA}.


% This mechanism accurately estimates the likelihood of probe-generated trajectories and applies appropriate corrections to ensure the bias of policy gradient as small as possible despite the mixed sampling from both the original policy and the probe distribution.

% ensure that policy gradients remain unbiased

% We implement REX-RAG using Group Relative Policy Optimization (GRPO)~\cite{DeepSeekMath} as the underlying reinforcement learning algorithm, which naturally accommodates the mixed trajectory sampling while maintaining computational efficiency.
Extensive experiments on multi-hop question answering ~\cite{colmerauer1990introduction} benchmarks demonstrate that REX-RAG significantly outperforms existing methods, achieving substantial improvements in both answer accuracy and reasoning quality.  On average, it outperforms strong baselines by 6.2\% on Qwen2.5-3B and 3.6\% on Qwen2.5-7B. Furthermore, as shown in Figure~\ref{fig:training_dynamics}, our analysis reveals that the framework successfully escapes dead ends while maintaining stable policy learning, with consistently higher success rates and lower dead end rates compared to self-reflection approaches, validating the effectiveness of our principled exploration strategy.

The main contributions of this work are:
\begin{itemize}
    \item We identify and formalize the \textit{dead end} problem in RL-based RAG training, demonstrating its significant impact on policy optimization and showing that it affects over 85\% of training instances in early phases.
    \item We propose \textbf{REX-RAG}, a novel framework that combines mixed sampling strategies with policy realignment mechanisms to enable effective exploration while maintaining training stability and unbiased policy updates.
    \item We provide comprehensive theoretical analysis and empirical validation, achieving substantial improvements over strong baselines (6.2\% on Qwen2.5-3B and 3.6\% on Qwen2.5-7B) and establishing new state-of-the-art results on multi-hop question answering benchmarks.
\end{itemize}

% The main contributions of this work are: (1) We identify and formalize the dead end problem in RL-based RAG training, demonstrating its significant impact on policy optimization; (2) We propose REX-RAG, a novel framework that combines mixed sampling strategies with policy realignment mechanisms to enable effective exploration while maintaining training stability; (3) We provide theoretical analysis and empirical validation showing that our approach significantly outperforms existing methods on multi-hop question answering tasks, establishing new state-of-the-art results on several benchmarks.
