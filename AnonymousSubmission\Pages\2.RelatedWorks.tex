\section{Related Work}

\paragraph{Retrieval-Augmented Generation (RAG).} 
RAG~\cite{RAG} has fundamentally transformed how language models access and utilize external knowledge. The RAG framework combines search engines with generative models, enabling LLMs to ground their responses in retrieved documents~\cite{arslan2024survey}. This paradigm has proven particularly effective for knowledge-intensive tasks where parametric knowledge alone is insufficient~\cite{PopQA}. 
For multi-hop reasoning tasks, several specialized approaches have emerged~\cite{Self-Reflection, gao2025synergizing}, for example,  IRCoT~\cite{IRCOT} interleaves retrieval with chain-of-thought reasoning, allowing models to iteratively gather evidence across multiple reasoning steps. 
However, these methods rely on supervised fine-tuning or simple prompting, limiting their capacity to learn optimal retrieval and reasoning through interaction.

\paragraph{Reinforcement Learning with Verifiable Rewards (RLVR).} 
RLVR has emerged as a popular approach for improving LLM reasoning.
The integration of RL and RAG has opened new avenues for training LLMs to perform complex reasoning tasks~\cite{Deepresearcher, mei2025O2, qian2025scent}. Recent advances in this area include reasoning-oriented models that employ RL to improve step-by-step reasoning capabilities~\cite{Zerosearch, MMSearch-R1, R3-RAG}. In the context of RAG, Search-R1~\cite{Search-r1} represents a pioneering effort to apply RL for training LLMs to dynamically interact with search engines. However, as noted in empirical studies~\cite{Search-r1-emperical}, existing RL approaches~\cite{R1-Searcher, R1-Searcher++} for reasoning-search interleaved agents face significant challenges in exploration efficiency and training stability.

