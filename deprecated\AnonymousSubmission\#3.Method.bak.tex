\section{Method}

\begin{figure*}[t]  % 使用 figure* 并设置 [t] 表示靠页面顶部
  \centering
  \includegraphics[width=0.9\textwidth]{figures/framework.pdf}
  \caption{Framework overview of our proposed method.}
  \label{fig:framework}
\end{figure*}

In this section, we introduce the task background and the framework of RLVR(\S\ref{}). We then present the detailed design of the simple yet effective REX-RAG framework, along with the probe distribution used to guide the training of RLVR (\S\ref{}). Finally, we describe the bias correction and dynamic sampling methods used to stabilize the training process (\S\ref{}).


% 在这一节中，我们首先介绍了任务背景以及所使用到的 reinforcement learning with verifiable rewards 框架。之后，我们介绍detailed design of 简单并且有效的 REX—RAG 框架，与其用于指导 RLVR 训练的探针分布 (\S \ref{}). 最后用于稳定训练的偏差矫正以及动态采样方法(\S \ref{}).

% 在这一小节，我们将展示 Probe-Searcher 的具体设计细节，包括训练流程和分布矫正方法。

% In this section, we present the detailed design of the Probe-Searcher, including training procedure and the distribution correction method.

% 在这一小节，我们将展示 Probe-Searcher 的具体设计细节。我们首先formulate 带有搜索引擎的RLVR 过程，之后，我们介绍Probe-Searcher的探针分布设计，以及偏移矫正方法

% In this section, we first formulate the reinforcement learning with verifiable rewards integrated with a search engine. Subsequently, we introduce the design of the probe distribution used by the REX-RAG, along with the shift correction methods.

% \subsection{RLVR with Search Engine}

% % 高级的 RAG 系统通过基于 LLM 的组件完成检索和推理的紧密结合，使得 LLM 可以自主分解问题并且调用检索工具，显著提升了探索外部知识以解决复杂问题的能力。而当前 RLVR 的新范式把检索动作和推理过程纳入可验证奖励的强化学习框架，使得模型可以在训练中学会自主检索。

% % Advanced RAG systems tightly integrate retrieval and reasoning through LLM-based components, enabling the LLM to autonomously decompose problems and invoke retrieval tools. This significantly enhances its ability to explore external knowledge in solving complex tasks. The emerging RLVR paradigm further incorporates retrieval actions and reasoning processes into a reinforcement learning framework with verifiable rewards, allowing the model to learn autonomous retrieval during training.

% % 该方程中定义的目标函数代表了一种正则化策略优化框架，其目标是学习一种策略\pi_\theta(y \mid x; \mathcal{R})，该策略在特定任务的奖励最大化与相对于参考策略的散度控制之间取得平衡。具体而言，模型旨在在数据分布\mathcal{D}以及基于输入x和额外信息\mathcal{R}从学习到的策略中抽取的行动样本上，最大化期望奖励\mathbb{E}_{x \sim \mathcal{D}, \, y \sim \pi_\theta(\cdot \mid x; \mathcal{R})} [r_\phi(x, y)]，其中\mathcal{R}通常表示诸如搜索引擎输出之类的上下文信号。奖励函数r_\phi(x, y)评估给定输入x时输出y的质量或相关性，并由\phi参数化。为防止学习到的策略与参考策略\pi_{\text{ref}}(y \mid x; \mathcal{R})过度偏离，引入了 KL 散度惩罚项\mathrm{D}_{\mathrm{KL}}[\pi_\theta(y \mid x; \mathcal{R}) \,\|\, \pi_{\text{ref}}(y \mid x; \mathcal{R})]。该项充当正则化项，促进稳定性并保留参考策略中编码的有用先验知识。这种正则化的强度由标量超参数\beta > 0控制。总之，该优化过程在利用奖励信号与保持与可信策略的接近程度之间取得平衡，从而在弱监督或间接监督的情况下实现安全有效的学习。

% % 基于 RLVR 的 RAG 方法，可以描述为给定一个策略 LLM $\pi_{\theta}$,和一个参考 LLM $\pi_{ref}$,训练策略 LLM在数据分布$\mathcal{D}$上基于输入$x$和搜索引擎$\mathcal{R}$提供的额外信息输出结果$y$用于最大化奖励函数的期望：

% \paragraph{Task Formulation}The RLVR-based RAG method can be formally described as follows: given a policy LLM $\pi_{\theta}$ and a reference LLM $\pi_{\text{ref}}$, the goal is to train the policy LLM $\pi_{\theta}$ to generate an output $y$ based on an input $x$ and additional information provided by a search engine $\mathcal{R}$, such that the expected value of a reward function over the data distribution $\mathcal{D}$ is maximized:

% \begin{align}
% \max_{\pi_\theta} \, &\mathbb{E}_{x \sim \mathcal{D}, \, y \sim \pi_\theta(\cdot \mid x; \mathcal{R})}
% \left[  r(x, y) \right] \nonumber \\
% &- \beta \, \mathrm{D}_{\mathrm{KL}}\left[
% \pi_\theta(y \mid x; \mathcal{R}) \,\|\, \pi_{\text{ref}}(y \mid x; \mathcal{R})
% \right],
% \end{align}
% where $\mathrm{D}_{\mathrm{KL}}$ serves as a regularization term to measure the Kullback–Leibler divergence between the policy LLM and the reference LLM, preventing the policy from deviating too far from the reference.

% 具体来说，数据集$\mathcal{D} = \{(q_i, a_i)\}_{i=1}^n$，中每个问题均包含一个问题$q$和答案$a$。在训练过程中，策略模型$\pi_{\theta}$会对每个问题rollout轨迹$o$。Follow Search-R1的提示词（详见附录\ref{}），我们通过鼓励模型将思考过程放在``"<think> \dots ``"</think>中，将对搜索引擎的查询放在``"<search> \dots ``"</search>。每当检测到 LLM 输出了<search>的标签，我们会将查询送入搜索引擎$\mathcal{R}$,并将返回的信息$d$通过``"<infomation> \dots ``"</information>插入到当前的推理轨迹当中。


% \subsection{Backgound}

% \paragraph{Task Formulation} In retrieval-augmented generation, a generator (typically an LLM) is tasked with answering a complex question \( q \), sampled from a dataset \( \mathcal{D} = \{(q_i, a_i)\}_{i=1}^{n} \). Each question requires reasoning and access to external knowledge. To answer \( q \), the LLM alternates between retrieval and generation, incrementally refining its reasoning at each step. The final answer is generated based on the input question and a set of retrieved documents \( d = \{d_1, d_2, \dots, d_k\} \) obtained from a search engine \( \mathcal{R} \).

% Given a complex question $q$ drawn from a dataset $\mathcal{D}=\{(q_i,a_i)\}_{i=1}^n$, which necessitates multi-step reasoning and the external knowledge, the task of retrieval-augmented generation requires a generator ~(typically a LLM) produce an answer conditoned on question $q$ and documents $d$ retrieved from search engine $\mathcal{R}$, by interleaving retrieval steps with token generation.

% In retrieval‑augmented generation, a generator (typically an LLM) is tasked with answering a complex question \(q\) drawn from a dataset \(\mathcal{D} = \{(q_i, a_i)\}_{i=1}^{n}\) that requires multi‑step reasoning and access to external knowledge. The LLM produces its answer on \(q\) and a set of documents \(d = \{d_1, d_2, \dots, d_k\}\) retrieved from a search engine \(\mathcal{R}\), interleaving retrieval rounds with token generation to iteratively refine the reasoning process.


% retrieval-augmented generation (RAG) task, where a policy language model (LLM) is tasked with producing an answer $y$ to a query $x$ by conditioning on external knowledge retrieved from a search engine $\mathcal{R}$. Concretely, given a dataset $\mathcal{D}=\{(q_i,a_i)\}_{i=1}^n$, each query $q$ is paired with a reference answer $a$. A vanilla RAG policy $\pi_\theta(y\mid x;\mathcal{R})$ generates output by interleaving retrieval steps with token generation, but it lacks an explicit mechanism to optimize for downstream task metrics.

% \paragraph{RLVR Retrieval-Augmented Generation} The emerging RLVR paradigm further incorporates retrieval actions and reasoning processes into a reinforcement learning framework with verifiable rewards, allowing the model to learn autonomous retrieval during training. 这里，我们使用GRPO作为实现 REX-RAG 框架的 RL 算法。具体来说， Let $\pi_{\mathrm{ref}}$ denote a fixed reference LLM. We seek to train the policy model $\pi_\theta$ to maximize the 以下目标函数:


% \begin{align}
% &\max_\theta J_{\mathrm{GRPO}}(\theta) = \mathbb{E}_{(q,a)\sim \mathcal{D},\,\{o_i\}_{i=1}^G\sim \pi_{\theta_{\mathrm{old}}}(\cdot\mid q)} \notag \\
% &\Biggl[\frac{1}{G}\sum_{i=1}^G\frac{1}{\lvert o_i\rvert}\sum_{t=1}^{\lvert o_i\rvert}
% \mathrm{clip}\!\Bigl(\frac{\pi_\theta(o_{i,t}\mid q,o_{i,<t})}
% {\pi_{\theta_{\mathrm{old}}}(o_{i,t}\mid q,o_{i,<t})},\,1-\epsilon,\,1+\epsilon\Bigr)\,\hat A_{i,t} \notag \\
% & - \beta\,D_{\mathrm{KL}}\bigl[\pi_\theta\Vert\pi_{\mathrm{ref}}\bigr] \Biggr],
% \end{align}
% 其中，${o_i}^{G}_{i=1}$是从旧策略模型中采样得到的轨迹，G 为组大小。 $\hat{A}_{i,t} = \frac{r_{i,t}-mean(r_t)}{std(r_t)}$是归一化的 token 级优势。$\epsilon$ 和 $\beta$是用于控制梯度裁剪比例和 KL 散度正则项的系数。可验证的奖励$r$,我们仅采用了最简单的exact match，防止奖励 hack：

% \begin{align}
%     r = \mathrm{EM}({ans}_{\mathrm{pred}},{ans}_{\mathrm{gold}})
% \end{align}
% where ${ans}_{\mathrm{pred}}$和${ans}_{\mathrm{gold}}$分别是模型输出轨迹$o$中提取出的答案和问题的 gound truth。

% \paragraph{RLVR Enhanced Retrieval-Augmented Generation}
% The emerging RLVR paradigm further integrates retrieval actions and reasoning processes into a reinforcement learning framework with verifiable rewards, enabling the model to learn autonomous retrieval strategies during training. Specifically, let $\pi_{\mathrm{ref}}$ denote a fixed reference LLM. Our goal is to train a policy model $\pi_\theta$ to maximize the following objective:

% % 这里还是放 RLVR

% \begin{align}
%   \max_{\theta}\; &\mathbb{E}_{(q,a)\sim\mathcal{D},\;y\sim\pi_\theta(\cdot\mid q;\mathcal{R})}\bigl[r(q,y)\bigr] \notag\\
%   & - \beta\,\mathrm{D}_{\mathrm{KL}}\bigl[\pi_\theta(y\mid q;\mathcal{R})\,\|\,\pi_{\mathrm{ref}}(y\mid q;\mathcal{R})\bigr],
% \end{align}
% where $r$代表了可验证的奖励。而 \(\mathrm{D}_{\mathrm{KL}}\) penalizes deviations from the reference model and the hyperparameter \(\beta>0\) governs the trade‑off between reward maximization and regularization.

\subsection{Preliminary}

\paragraph{RAG Task Formulation} RAG addresses this limitation of LLMs when answering complex questions that require external knowledge beyond their training data. Formally, given a question $q$ sampled from a dataset $\mathcal{D} = \{(q_i, a_i)\}_{i=1}^{n}$, the LLM engages in an process of alternating between generation and retrieval. At each step, the model produces some reasoning text or a search query and uses it to retrieve a set of documents $d = \{d_1, d_2, \dots, d_k\}$ from an external knowledge source (such as a search engine or database $\mathcal{R}$). Ultimately, the final answer is generated based on the question along with the content of the retrieved documents.

\paragraph{RLVR Enhanced RAG} RLVR extends the RAG framework by integrating retrieval and reasoning into a reinforcement learning loop, enabling models to autonomously learn retrieval strategies via trial-and-error. The learning process is guided by a verifiable reward signal based on an objective correctness criterion, such as exact match.
Formally, for each question-answer pair $(q,y)$, the reward signal $r(q,y)$ provides feedback indicating whether the generated answer satisfies predefined verification criteria. % (e.g., exact match with ground truth)
% Through this reward-guided learning process, the model is incentivized to develop effective retrieval strategies and reasoning capabilities that maximize the probability of generating verifiably correct answers.

% whether the answer $y$ to a question $q$ matches the ground truth or satisfies predefined checks. RLVR training thus incentivizes the model to both retrieve effectively from $\mathcal{R}$ and generate accurate responses to maximize this reward.

\paragraph{GRPO Algorithm}
GRPO is an emerging reinforcement learning (RL) algorithm that is gaining increasing attention for training large language model (LLM) policies. It eliminates the need for value networks by leveraging normalized rewards across multiple trajectories as advantage estimates, while ensuring policy stability through clipping and Kullback–Leibler (KL) divergence regularization.
Formally, consider a fixed reference policy $\pi_{\mathrm{ref}}$ (e.g., the pre-trained LLM before reinforcement learning). GRPO trains a target policy LLM $\pi_\theta$ to maximize the expected reward while remaining close to the reference policy for stability, leveraging trajectories generated by behavior policy $\mu$ (In the original paper, the behavioral policy is the policy model before gradient update in each training step). The training objective is given by:

\begin{align}
\label{GRPO_target}
&\max_\theta J_{\mathrm{GRPO}}(\theta) = \mathbb{E}_{(q,a)\sim \mathcal{D},\,\{o_i\}_{i=1}^G\sim \mu(\cdot\mid q)} \notag \\
&\Biggl[\frac{1}{G}\sum_{i=1}^G\frac{1}{\lvert o_i\rvert}\sum_{t=1}^{\lvert o_i\rvert}
\mathrm{clip}\Bigl(\frac{\pi_\theta(o_{i,t}\mid q,o_{i,<t})}
{\mu(o_{i,t}\mid q,o_{i,<t})},\,1-\epsilon,\,1+\epsilon\Bigr)\,\hat A_{i,t} \notag \\
&\quad - \beta\,D_{\mathrm{KL}}\bigl[\pi_\theta\Vert\pi_{\mathrm{ref}}\bigr] \Biggr],
\end{align}
where $\frac{\pi_\theta(o_{i,t}\mid q,o_{i,<t})}
{\mu(o_{i,t}\mid q,o_{i,<t})}$ denotes the importance sampling ratio. The KL divergence term $\mathrm{D}_{\mathrm{KL}}$ penalizes the policy for deviating excessively from the reference model. The hyperparameter $\beta$ controls the trade-off between reward maximization and regularization.
% 这里应该要改成 GRPO 的优化目标
% \begin{align}
%   \max_{\theta}\; &\mathbb{E}_{(q,a)\sim\mathcal{D},\;y\sim\pi_\theta(\cdot\mid q;\mathcal{R})}\bigl[r(q,y)\bigr] \notag\\
%   & - \beta\,\mathrm{D}_{\mathrm{KL}}\bigl[\pi_\theta(y\mid q;\mathcal{R})\,\|\,\pi_{\mathrm{ref}}(y\mid q;\mathcal{R})\bigr],
% \end{align}


% 将这部分改为 GRPO


% \begin{align}
%   \max_{\theta}\; &\mathbb{E}_{(q,a)\sim\mathcal{D},\;y\sim\pi_\theta(\cdot\mid q;\mathcal{R})}\bigl[r(q,y)\bigr] \notag\\
%   & - \beta\,\mathrm{D}_{\mathrm{KL}}\bigl[\pi_\theta(y\mid q;\mathcal{R})\,\|\,\pi_{\mathrm{ref}}(y\mid q;\mathcal{R})\bigr],
% \end{align}
% where \(\mathrm{D}_{\mathrm{KL}}\) penalizes deviations from the reference model and the hyperparameter \(\beta>0\) governs the trade‑off between reward maximization and regularization.




% To expose the model’s internal deliberations and interface with search engine \(\mathcal{R}\), we follow previous work ~\cite{}, employ special markup:Internal reasoning steps are enclosed in ``\texttt{<think>…</think>}'' tags, Search instructions are emitted as ``\texttt{<search>…</search>}'' and Final answer are emitted as ``\texttt{<answer>…</answer>}''

% Whenever the policy emits a ``\texttt{<search>}'' tag, the enclosed query is sent to \(\mathcal{R}\), which returns a set of documents \(d\). These retrieved documents are then injected back into the trajectory as ``\texttt{<information>…</information>}'' tokens, allowing the policy to condition on up‑to‑date evidence. 当输出``\texttt{<answer>}''标签时，generation 将被终止。


\subsection{REX-RAG framework}

In this work, we introduce REX-RAG, a novel framework that implements systematic exploration through mixed sampling from both the current policy and a virtual probe policy. This design encourages continuous exploration of alternative reasoning paths even in suboptimal regions, while maintaining learning stability through a Policy Realignment Mechanism that constructs the virtual policy distribution via principled adjustments.

As illustrated in Fig. \ref{fig:framework}, the framework (Fig. \ref{fig:framework}(a)) operates in two phases: Rollout Phase (Fig. \ref{fig:framework}(b)) that employs Mixed Sampling Strategy to generate diverse trajectories by blending actions from both policies, and Update Phase (Fig. \ref{fig:framework}(c)) that applies Policy Realignment to refine importance ratios, ensuring approximately unbiased gradient estimates while incorporating insights from exploratory rollouts.




% In this work, we propose REX-RAG, a novel framework that introduces a systematic exploration mechanism by performing mixed sampling from both the current policy and a virtual probe policy. This design encourages continuous exploration of alternative reasoning trajectories, even when the policy falls into suboptimal regions. To maintain learning stability and correctness, we further employ a novel Policy Mapping Mechanism that constructs the distribution of virtual policy through principled distributional adjustments, which can be used to get an approximate estimate of policy gradient.


% To maintain learning stability and correctness, we further employ a Policy Correction Mechanism that realigns the policy with the reference model through principled distributional adjustments.

% aligned with the reference behavior

% systematically explores alternative reasoning paths while maintaining rigorous policy learning through principled distributional corrections. The key innovation of REX-RAG lies in its lightweight model-prompt alternation mechanism. Rather than training additional parameterized models or relying on simple self-reflection that produces limited variations, our framework employs a curated collection of chain-of-thought prompts to inject diverse reasoning directions when trajectories fail. When the policy encounters a dead end—indicated by an incorrect answer—we strategically insert concise reasoning hints from a pre-constructed prompt pool and resume generation, effectively steering the model toward unexplored solution paths.

% we adopt the Group Relative Policy Optimization as the reinforcement learning algorithm to implement the REX-RAG framework. As illustrated in Fig. ~\ref{fig:framework} (a), for each input query, the we first sample a set of trajectories $\mathcal{O} = \{o_i\}^{G}_{i=1}$ using policy model before parameter updates as behavior policy. Unlike GRPO's predecessor (i.e., PPO), GRPO does not rely on a value network or generalized advantage estimation to compute advantages. Instead, for a given query $q$, we generates multiple trajectories through rollouts and computes a normalized reward as the advantage:

% \begin{align}
%   \hat{A}_{i,t} = \frac{r_{i,t}-\mathrm{mean}(r_t)}{\mathrm{std}(r_t)},
% \end{align}
% where $\hat{A}_{i,t}$ denotes the advantage of the $t$-th token in the $i$-th trajectory.
% Finally, the algorithm optimizes the following objective:

% \begin{align}
%   \label{grpo_target}
%   \max_{\theta} \ & \mathcal{J}_{\text{GRPO}}(\theta) =
%   \mathbb{E}_{(q, a) \sim \mathcal{D}, \{o_i\}_{i=1}^G \sim \pi_{\theta_{\text{old}}}(\cdot|q)} \notag \\
%   & \frac{1}{G} \sum_{i=1}^G \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
%   \Bigg\{
%     \min \Bigg[
%       \frac{\pi_\theta(o_{i,t} \mid q, o_{i,<t})}{\pi_{\theta_{\text{old}}}(o_{i,t} \mid q, o_{i,<t})} \hat{A}_{i,t}, \notag \\
%       & \text{clip}\left( \frac{\pi_\theta(o_{i,t} \mid q, o_{i,<t})}{\pi_{\theta_{\text{old}}}(o_{i,t} \mid q, o_{i,<t})}, 1-\epsilon, 1+\epsilon \right) \hat{A}_{i,t}
%     \Bigg] \notag \\
%     &- \beta D_{\text{KL}}\left[ \pi_\theta \| \pi_{\text{ref}} \right]
%   \Bigg\} ,
% \end{align}
% with the coefficient $\epsilon$ controls the clipping range to prevent excessively large updates to the policy at each timestep.

% \begin{align}
% &\max_\theta J_{\mathrm{GRPO}}(\theta) = \mathbb{E}_{(q,a)\sim \mathcal{D},\,\{o_i\}_{i=1}^G\sim \pi_{\theta_{\mathrm{b}}}(\cdot\mid q)} \notag \\
% &\Biggl[\frac{1}{G}\sum_{i=1}^G\frac{1}{\lvert o_i\rvert}\sum_{t=1}^{\lvert o_i\rvert}
% \mathrm{clip}\!\Bigl(\frac{\pi_\theta(o_{i,t}\mid q,o_{i,<t})}
% {\pi_{\theta_{\mathrm{b}}}(o_{i,t}\mid q,o_{i,<t})},\,1-\epsilon,\,1+\epsilon\Bigr)\,\hat A_{i,t} \notag \\
% &\quad - \beta\,D_{\mathrm{KL}}\bigl[\pi_\theta\Vert\pi_{\mathrm{ref}}\bigr] \Biggr],
% \end{align}


% 在这个工作中，我们使用组相对策略优化 GRPO 算法作为实现 REX-RAG 框架的强化学习算法。和图\ref{fig:framework}描述的一样，对于每一个问题，GRPO 算法会使用行为策略$\pi_\mathrm{b}$采样得到一组轨迹$\mathcal{O} = \{o_i\}^{G}_{i=1}$。与其前身近端策略优化 PPO 算法不同，在计算优势时，其没有采用价值网络和广义优势估计，而是采用了对于一个问题$q$rollout 出多个回答，使用正则化奖励$\hat{A}_{i,t} = \frac{r_{i,t}-\mathrm{mean}(r_t)}{\mathrm{std}(r_t)}$作为优势，得到每个 token 上的优势值。最后，优化以下表达式：

% \begin{align}
% &\max_\theta J_{\mathrm{GRPO}}(\theta) = \mathbb{E}_{(q,a)\sim \mathcal{D},\,\{o_i\}_{i=1}^G\sim \pi_{\theta_{\mathrm{b}}}(\cdot\mid q)} \notag \\
% &\Biggl[\frac{1}{G}\sum_{i=1}^G\frac{1}{\lvert o_i\rvert}\sum_{t=1}^{\lvert o_i\rvert}
% \mathrm{clip}\!\Bigl(\frac{\pi_\theta(o_{i,t}\mid q,o_{i,<t})}
% {\pi_{\theta_{\mathrm{b}}}(o_{i,t}\mid q,o_{i,<t})},\,1-\epsilon,\,1+\epsilon\Bigr)\,\hat A_{i,t} \notag \\
% &\quad - \beta\,D_{\mathrm{KL}}\bigl[\pi_\theta\Vert\pi_{\mathrm{ref}}\bigr] \biggr],
% \end{align}
% where coefficient $\epsilon$ governs the clipping ratio 防止单步策略变化过大。

% where $\{o_i\}_{i=1}^{G}$ denotes trajectories sampled from the old policy model, and $G$ is the group size. The term $\hat{A}_{i,t} = \frac{r_{i,t}-\mathrm{mean}(r_t)}{\mathrm{std}(r_t)}$ represents the normalized token-level advantage. The coefficients $\epsilon$ and $\beta$ control the clipping ratio and the strength of the KL divergence regularization, respectively.

% 再想想结构

% \subsubsection{Rollout Stage}


% \paragraph{RAG-Based Generation}

% \paragraph{Mixed Sampling Strategy}


% \subsubsection{Update Stage}

% \paragraph{Policy Correction Mechanism} To ensure the policy remains aligned with the reference model while exploring new reasoning paths, REX-RAG employs a Policy Correction Mechanism. This mechanism corrects the distribution of the policy model by introducing a virtual probe distribution $\pi_\mu$, which guides exploration and helps the policy escape from local optima. The probe distribution is realized through a lightweight model-prompt alternation, where the policy generates a trajectory and, if it fails to produce a correct answer, a concise chain-of-thought hint is injected to resume generation.


% \paragraph{Training Procedure}


% \paragraph{Learning under Guidance of Probe Distribution}







% 这里放 GRPO和 rollout generation 部分

% \paragraph{Rollout Generation} To make the model’s internal deliberations transparent and facilitate interaction with the search engine \(\mathcal{R}\), we adopt a tag-based method. Specifically, internal reasoning steps are encapsulated within ``\think{<think> \dots </think>}'' tags, search queries are denoted using ``\search{<search> \dots </search>}'' tags, and final answers are output within ``\answer{<answer> \dots </answer>}'' tags.

% When the policy emits a pair of ``\search{<search>}'' tag, the enclosed query is sent to \(\mathcal{R}\), which returns a set of relevant documents \(d\). These retrieved documents are then injected back into the trajectory as ``\information{<information> \dots </information>}'' block, enabling the model to condition its reasoning on up-to-date external evidence. Once the ``\answer{<answer>}'' block is produced, the generation process is terminated.


% \paragraph{Learning under Guidance of Probe Policy}
% In retrieval-augmented generation, RLVR training can stall at \emph{dead ends}: high‑reward trajectories exist, yet the current policy $\pi_\theta$ assigns them negligible probability, so gradient updates focus on low‑value trajectories.

% To restore exploration, REX‑RAG introduces a virtual probe distribution $\pi_\mu$. At every iteration we draw a mixture of $\alpha G$ trajectories from the policy LLM $\pi_{\theta}$ and $(1-\alpha)G$ exploratory trajectories from $\pi_\mu$, where the coefficient $\alpha\in(0,1)$ balances exploitation against exploration.

% To encourage exploration, REX-RAG replaces the original single behavior policy with a mixed sampling approach, drawing trajectories from both the current policy and a newly introduced probe policy based on an exploratory action distribution. Formally, the behavior policy set is defined as $\pi_{\mathrm{b}} = \{\pi_{\theta}, \pi_{\mu}\}$, where $\pi_{\theta}$ denotes the current policy LLM and $\pi_{\mu}$ represents the virtual probe distribution. At each training step, $\alpha G$ trajectories are sampled from $\pi_{\theta}$ and $(1 - \alpha)G$ exploratory trajectories are drawn from $\pi_{\mu}$, with $\alpha$ controlling the trade-off. As a result, the trajectories sampled for a given query $q_t$ consist of a mixture guided by both policies.



To restore exploration, REX-RAG abandons the original single behavior policy in favor of mixed sampling, drawing trajectories from both the policy model and a newly introduced probe policy defined over an exploratory action distribution. Formally, the behavior-policy set becomes:

\begin{align}
  \mu = \{\pi_{\theta}, \pi_{\varepsilon}\},
\end{align}
where $\pi_{\theta}$ denotes the target policy and $\pi_{\varepsilon}$ denotes the virtual probe distribution. At every training step, we therefore draw $\alpha G$ trajectories from $\pi_{\theta}$ and $(1-\alpha)G$ exploratory trajectories from $\pi_{\varepsilon}$, with $\alpha$ governing the trade-off. Thus, the trajectories sampled from query $q_t$ become:

\begin{align}
  \mathcal{O}_t &= \bigl\{\, o_{i} \mid o_{i} \sim \pi_{\theta} \,\bigr\}_{i=1}^{\alpha G} \;\cup\; \bigl\{\, o^{\prime}_{j} \mid o^{\prime}_{j} \sim \pi_{\varepsilon} \,\bigr\}_{j=1}^{(1-\alpha)G}
\end{align}




% 再引入无偏估计器。。。。。

\paragraph{Realization of Probe Policy} As shown in Fig. \ref{fig:framework} (b), the probe policy is realized through a lightweight \textsc{model-prompt} alternation. The policy first generates a trajectory; if the interim answer fails the exact-match check, we inject a concise chain-of-thought hint sampled from a fixed prompt pool\footnote{The pool is obtained by rephrasing a comprehensive reflection prompt into $k$ short COT fragments with GPT-4o. The complete reflection prompts and the $k$ COT fragments can be found in Appendix.
} and resume generation with $\pi_{\theta}$, yielding an exploratory trajectory $o^{\prime}$.


% 这里放一个算法

\begin{figure}[thbp]
  % \centering

  % 自定义带颜色的 tcolorbox
  \begin{tcolorbox}[
      enhanced,
      colback=gray!5,        % 背景色
      colframe=black!75!white,% 边框色
      boxrule=0.8pt,         % 边框粗细
      width=0.47\textwidth,
      title={},
      fonttitle=\bfseries,
      coltitle=black,
      % before skip=10pt,
      % after skip=10pt
]

    % 在这里插入你的自定义内容
    \think{<think>} Revisiting carefully, perhaps errors or oversights went unnoticed earlier.
  \end{tcolorbox}
  \caption{Example COT fragment prompting the LLM}
\end{figure}



We subsequently apply GRPO to the combined set of policy and probe trajectories, evaluating token-level losses on both the tokens generated by policy model and those introduced via the injected COT fragments. This design compels the model not only to condition its reasoning on the injected fragments, but also to internalize the underlying reasoning schema, thereby enabling autonomous discovery of further high‑value trajectories.



% \STATE $C[p] \gets \mathbf{0}_{|V|}$ for every  prefix $p$ in K





% \begin{algorithm}[tb]
% \caption{Build Prefix Frequency Model and Query PMF}
% \label{alg:prefix_pmf}
% \textbf{Input}: Tokenized sequences $K = \{k_1, k_2, \dots, k_n\}$\\
% \textbf{Output}: Prefix index mapping and PMF values
% \begin{algorithmic}[1]
% \STATE Initialize empty vocabulary set $V$
% \FOR{each token sequence $k$ in $K$}
%     \FOR{each token $t$ in $k$}
%         \STATE Add $t$ to $V$
%     \ENDFOR
% \ENDFOR
% \STATE Construct vocabulary index map $I_v[t] \gets$ index of $t$ in $V$
% \STATE Initialize empty prefix index map: $I_p[prefix] \gets$ zero vector of size $|V|$

% \FOR{each token sequence $k$ in $K$}
%     \FOR{$i=0$ to $|k|-2$}
%         \STATE $prefix \gets (k_0, k_1, \dots, k_i)$
%         \STATE $next \gets k_{i+1}$
%         \STATE $I_p[prefix][I_v[next]] \gets I_p[prefix][I_v[next]] + 1$
%     \ENDFOR
% \ENDFOR

% \FOR{each $prefix$ in $I_p$}
%     \STATE $counts \gets I_p[prefix]$
%     \STATE $total \gets \sum counts$
%     \FOR{$j=0$ to $|V|-1$}
%         \STATE $counts[j] \gets counts[j] / total$
%     \ENDFOR
% \ENDFOR

% \STATE \textbf{function} \texttt{QueryPMF}$(prefix, next\_token)$
% \STATE \quad \textbf{return} $I_p[prefix][I_v[next\_token]]$
% \end{algorithmic}
% \end{algorithm}

\begin{algorithm}[ht]
  \caption{PMF Construction via Frequency Distribution}
  \label{alg:build_pmf_model}
  \textbf{Input}: tokenizer $\mathcal{T}$; prompt set $\mathcal{P}=\{s_1,\dots,s_m\}$\\
  \textbf{Output}: function $\mathrm{PMF}(p,x)$
  \begin{algorithmic}[1]
    \STATE $K \gets \bigl\{\mathcal{T}(s)\;|\;s\in\mathcal{P}\bigr\}$ \COMMENT{tokenise every prompt}
    \STATE $V \gets$ unique tokens in $K$  \COMMENT{initialize vocabulary}
    \FORALL{$k \in K$}  \FORALL{$i < |k|-1$}
    \STATE $p \gets k_{0:i}$
    \STATE $C[p] \gets \mathbf{0}_{|V|}$ \COMMENT{initialize frequency distribution}
    \ENDFOR\ENDFOR
    \FORALL{$k \in K$}  \FORALL{$i < |k|-1$}
    \STATE $p \gets k_{0:i}$ ;\; $x \gets k_{i+1}$
    \STATE $C[p][V.\text{index(x)}] \mathrel{+}= 1$
    \ENDFOR\ENDFOR
    \STATE
    \STATE \textbf{define} $\mathbf{function}\;\mathrm{PMF}(p,x)$
    \STATE \hspace{1em}$\text{counts} \gets C[p]$
    \STATE \hspace{1em}\textbf{return} $\dfrac{\text{counts}[V.\text{index(x)}]}{\sum \text{counts}}$
    \STATE
    \STATE \textbf{return} $\mathrm{PMF}$ \COMMENT{exposes the query function to the caller}
  \end{algorithmic}
\end{algorithm}


\paragraph{Multiple Importance Sampling Estimate}

Since our sampling strategy has changed from a single behavior policy to two, our gradient estimation has been accordingly modified. For the sake of analytical simplicity, and by ignoring the KL divergence regularization term and the clipping technique in Equation~\eqref{GRPO_target}, the original optimization objective can be formalized as:

\begin{align}
  J(\theta) &=
  \mathbb{E}_{\substack{(q, a) \sim \mathcal{D}\\
  (o_i)_{i=1}^{G}\sim \mu(o \mid q)}}
  \left[
    \frac{1}{G}\sum_{i=1}^{G}\frac{1}{|o_i|}
    \sum_{t=1}^{|o_i|}
    \omega_{i,t}\,
    \hat{A}_{i,t}
  \right],
\end{align}
where $\omega_{i,t}$ denotes the importance sampling ratio that reweights samples from behavior policy $\mu$ to estimate gradients for target policy $\pi_{\theta}$:
\begin{align}
  \omega_{i,t} = \frac{\pi_{\theta}(o_{i,t}\mid q_i, o_{i,<t})}{\mu(o_{i,t}\mid q_i, o_{i,<t})}
\end{align}
The term $\hat{A}_{i,t}$ represents the normalized token-level advantage, defined as:

\begin{align}
  \hat{A}_{i,t} = \frac{r_{i,t} - \mathrm{mean}(r_t)}{\mathrm{std}(r_t)},
\end{align}
where $r_{i,t}$ is the reward assigned to token $t$ in trajectory $i$, and the normalization is performed across all tokens at position $t$ within the current batch to ensure stable training dynamics.

Thus, the derivative of the objective with respect to the parameter $\theta$ is given by:
\begin{align}
  & \nabla_\theta J(\theta) \notag \\
  &= \mathbb{E}_{\mu} \left[
    \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
    \omega_{i,t}\,
    \hat{A}_{i,t}\,
    \nabla_\theta \log \pi_\theta(o_{i,t} \mid q_i, o_{i,<t})
  \right]
\end{align}

Since $\mu$ comprises two behavior policies, we employ multiple importance sampling along with the balance heuristic (derivation in Appendix) to obtain an estimate of above expectation:

\begin{align}
  \hat{g}(\theta) =  \frac{1}{G}\sum^{G}_{i=1}\frac{1}{|o_i|}\sum^{|o_i|}_{t=1} \omega_{i,t} \hat{A}_{i,t}
  \nabla_\theta \log \pi_\theta(o_{i,t} \mid q_i, o_{i,<t}),
\end{align}
where the importance sampling ratio is as follows:
\begin{align}
  \omega_{i,t} = \frac{
    \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})
  }{
    \alpha \, \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})
    + (1 - \alpha) \, \pi_{\varepsilon}(o_{i,t} \mid q_i, o_{i,<t})
  }
\end{align}

The subsequent question is: how can we define the distribution of this virtual probe policy? To address this, we sample a trajectory \( o^{\prime}_{i} \) from \( \pi_\varepsilon \), and decompose it into three components: \( o'_{\mathrm{prompt}} \), \( o'_{\mathrm{before}} \), and \( o'_{\mathrm{after}} \), which respectively represent the inserted COT fragment, the segment preceding the COT fragment, and the segment following it. The distribution of these three parts is defined as follows:

\begin{align}
  \pi_{\varepsilon}&(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t}) = \begin{cases}
    \dfrac{\pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t})}{z^{1/|o'_{\mathrm{before}}|}}, 
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{before}} \\[1.2em]
    \mathrm{PMF}(o^{\prime}_{i<t}, o^{\prime}_{i,t}), 
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{prompt}} \\[1.2em]
    \pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t}), 
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{after}}
  \end{cases}
\end{align}




For the case where \( o^{\prime}_{i,t} \in o'_{\mathrm{before}} \), the term \( z \) denotes the error rate—i.e., the proportion of trajectories sampled from \( \pi_{\theta} \) at the current training step that result in errors. Since our virtual policy is intended to simulate sampling only from the error-prone regions of \( \pi_{\theta} \), we approximate this by assuming that the probability mass over the \( o'_{\mathrm{before}} \) segment is rescaled accordingly. Specifically, we roughly approximate the virtual probe policy by normalizing each token probability in the \( o'_{\mathrm{before}} \) by \( z^{1 / \mid o'_{\mathrm{before}} \mid} \), reflecting the assumption that such trajectories are representative of the erroneous subset of the policy LLM's behavior.

When \( o^{\prime}_{i,t} \in o'_{\mathrm{prompt}} \), the corresponding distribution is approximated using the empirical frequency distribution derived from the prompt pool. This serves as an estimate of the distribution induced by uniformly sampling prompts from the pool. The specific simulation procedure and the associated probability mass function (PMF) are detailed in Algorithm~\ref{alg:build_pmf_model}.

Lastly, since the sampling in this segment is performed directly using \( \pi_{\theta} \) without any additional constraints or manually defined modifications, we adopt \( \pi_{\theta} \) itself as the corresponding probability distribution.





% 由于我们是在轨迹出错时才会添加注入提示词，所以\( o'_{\mathrm{before}} \)可以认为是从原始概率分布中满足条件的区域中采样得到的。这个虚拟分布和



% 由这个表达式来看，我们的轻量级的设计，还避免了额外引入参数化模型而导致的计算重要采样系数的计算量增加。

% 这样，我们必须要合法地定义出这个虚拟探针策略所对应的分布。


% 用如\ref{}算法的简单方式，获得了对应的额 bucket分布




% \begin{align}
% \nabla_\theta\widehat{\mathcal J}(\theta) &=
% \frac{1}{G}\sum_{i=1}^{G}\frac{1}{|o_i|}
% \sum_{t=1}^{|o_i|}
% \frac{\pi_\theta\!\bigl(o_{i,t}\mid q,\;o_{i,<t}\bigr)}
%      {\pi_{\theta_{\mathrm{old}}}\!\bigl(o_{i,t}\mid q,\;o_{i,<t}\bigr)}
% \,\hat A_{i,t}\;
% \nabla_\theta\log\pi_\theta\!\bigl(o_{i,t}\mid q,\;o_{i,<t}\bigr).
% \end{align}

% 首先由于大模型使用 softmax 函数建模概率分布，我们可以认为 LLM 的支持集是覆盖全部可能序列的。

% 在这里，虽然我们的探测策略在某些序列上的概率为 0。我们采用了 balance heuristic

\paragraph{Dynamic Sampling}

Moreover, to ensure effective training, we desire that the distribution of virtual probe policy, especially the inserted prompt tokens, should not significantly deviate from our target policy $\pi_\theta$ . This constraint prevents situations where the inserted tokens exhibit extremely low probabilities under $\pi_\theta$, which would consequently yield very small importance sampling ratio. In such cases, even if the advantage of the inserted trajectory is positive, the excessively small importance sampling ratio would result in negligible gradient updates.


% 为此，我们引入了动态采样策略，对于每个训练 step，我们会过量从探测策略$\pi_\mu$中采样。如图\ref{fig:framework}(c)所示，对于使用虚拟分布插入的提示词部分，我们会使用当前的策略模型计算提示词中每个 token 的平均概率。之后，我们会将平均概率最低的直接抛弃，保留相对较高的$(1-\alpha)G$个轨迹。

To this end, we introduce a dynamic sampling strategy. For each training step, we deliberately oversample from the exploration policy $\pi_{\text{probe}}$. As illustrated in Fig. ~\ref{fig:framework}(c), for the COT fragment inserted using the virtual policy, we compute the average probability of each token in the fragment under the target policy. Subsequently, we discard the trajectories with the lowest average probabilities and retain the top $(1 - \alpha)G$ trajectories with relatively higher probabilities.





% Moreover, for the sake of training efficiency, we aim for the virtual probe distribution—particularly the inserted prompt tokens—to not deviate too far from our policy model. This is to avoid scenarios where these tokens have extremely low probabilities under $\pi_\theta$, which would lead to very small importance sampling weights. In such cases, even if the inserted trajectories have positive advantages, the gradient updates would be negligible due to the suppressed importance weights.

% 此外，出于训练有效性的考虑，我们希望这个虚拟的探测分布，尤其是插入的提示词部分，不会和我们的策略模型距离太远，以避免出现这些 token 在 $\pi_\theta$上概率过低，进而重要性采样系数过低，这样即使我们插入的轨迹优势为正，而因为重要性采样系数过低，也几乎不会产生梯度更新。

% 我们希望重要性采样系数可以不会变化过于剧烈。所以，我们计划，第一，

\paragraph{Reward Modeling}
For the verifiable reward \( r \), we adopt exact match as the reward signal, as its simplicity mitigates the risk of reward hacking. Specifically, we compare the predicted answer extracted from the ``\answer{<answer> \dots </answer>}'' block in the trajectory with the known correct answers. This can be formalized as:

\begin{align}
  r =
  \begin{cases}
    1, & \text{if } \mathrm{EM}({a}_{\text{pred}}, {a}_{\text{gold}})\\
    0, & \text{otherwise}
  \end{cases},
\end{align}
where \( {a}_{\text{pred}} \) and \( {a}_{\text{gold}} \) denote the predicted and gold-standard answers, respectively.

% 三个部分的概率分布定义






% \paragraph{Reward Modeling} For the verifiable reward $r$, we adopt the simplest form of exact match to avoid reward hacking:

% \begin{align}
%     r = \mathrm{EM}({ans}_{\mathrm{pred}}, {ans}_{\mathrm{gold}})
% \end{align}

% where ${ans}_{\mathrm{pred}}$ and ${ans}_{\mathrm{gold}}$ denote the answer extracted from the trajectory $o$ and the ground truth answer to the query, respectively.


% 最下面放奖励建模




% By restricting the balance‑heuristic estimator to just two proposals—the policy and the prompt‑augmented probe—we reduce the evaluation cost from $\mathcal{O}(NJ)$ to $\mathcal{O}(N)$, eliminating the need to score each trajectory under all $J$ proposals.



% To mitigate the ``dead‑end'' exploration problem—high‑reward trajectories that receive negligible probability mass under the current policy—REX‑RAG augments training with a virtual \emph{probe distribution} $\pi_\mu$. During each update we sample a mixture of $\alpha G$ \emph{policy} trajectories from $\pi_\theta$ and $(1-\alpha)G$ \emph{exploratory} trajectories from $\pi_\mu$. Concretely, the policy first rolls out a partial trajectory $o$. If the intermediate answer fails the exact‑match check, we draw a concise chain‑of‑thought hint from a fixed prompt pool\footnote{The pool is built by decomposing a comprehensive reflection prompt into $k$ short natural‑language hints using GPT‑4o.} and insert it into the context; generation then resumes with $\pi_\theta$, yielding an exploratory trajectory $o'$. This simple \textsc{model‑prompt} alternation implements $\pi_\mu$ without introducing new parameters and circumvents the $\mathcal{O}(NJ)$ overhead of evaluating every trajectory under every proposal in classical multiple‑importance sampling.

% The union of policy and probe trajectories is optimized with GRPO: we compute the token‑level loss on \emph{all} tokens—including those originating from the injected hints—so that $\pi_\theta$ not only conditions on but also learns to imitate productive exploratory steps. Empirically, the probe‑guided mixture steers learning away from dead ends and accelerates convergence compared with a pure on‑policy baseline.


% 我们采用 GRPO 作为实现 REX-RAG 框架的 RL 算法。GRPO 算法的优化目标如下：

% As mentioned above, retrieval-augmented generation methods built on the RLVR framework are prone to encountering ``dead ends,'' which impede effective policy exploration. We attribute this issue to the fact that certain high‐value trajectories—and the actions leading to them—have low probability under the original behavior policy.

% To address this issue, inspired by the multiple importance sampling, we introduce a virtual probe distribution $\pi_\mu$ in REX-RAG to guide the learning process of the policy model. According to the Fig. \ref{fig:framework}, this probe distribution collaborates with the current policy model to jointly sample data, which is then used for gradient estimation during training. During training, the policy LLM $\pi_\theta$ generates normal trajectories $o$, while the probe distribution $\pi_\mu$ produces exploratory trajectories $o^{\prime}$. The proportions of these two types of trajectories are controlled by $\alpha$ and $1 - \alpha$, respectively.

% 在多重重要性采样中，对于常用的balance heuristic估计器，需要计算全部轨迹在全部采样策略上的概率，时间复杂度$O(NJ)$，N 为轨迹数量，J 为采样策略数量(在每个轨迹计算概率的时间复杂度为 O(1)的前提下)。与其引入新的参数化模型，我们选择了使用当前策略模型和提示词 pool 进行交替采样的方法作为探针分布的实现。


% 如图\ref{fig:framework} (b)所描述的，$\pi_\mu$采用了一种策略模型和提示词 pool 进行交替采样的方法。简单来说，我们会先使用策略模型$\pi_{\theta}$进行正常的生成，如果该轨迹中产生了$ans$部分。我们会判断答案是否和标准答案一致。如果一致则结束采样。如果不一致，我们会从预先定义的提示词池中采样一段完整的提示词插入推理轨迹中，并继续使用策略模型$\pi_{\theta}$完成推理。之后，这些由虚拟探针分布采样出的轨迹会和策略模型的轨迹一起用 GRPO 算法对策略模型进行参数更新。值得注意的是，与插入的 information 不同，我们会对这些插入的提示词同样计算损失，而不仅仅是仅要求策略模型可以condition on 进行推理。

% 而这个提示词 pool，如表\ref{}所展示的示例，我们使用 ChatGPT 4o 将一段复杂的反思提示词转换为 k 个简短并且自然COT 片段，用以引导模型推理探索。





% 对于正常的轨迹，分为几个部分，document 是不计算损失的

% 探针分布设计，


% REX-RAG框架

% 为了提高探索效率，受到多重重要性采样方法的启发，在REX-RAG 中，我们使用一个虚拟的探针分布$\pi_\mu$来指导策略模型的学习过程。这个探针分布与当前的策略模型共同采样数据并用于模型的梯度估计过程。在训练过程中，策略 LLM $\pi_\theta$产生普通轨迹$o$,探针分布$\pi_\mu$产生探索轨迹$o^{\prime}$。其中两类轨迹的占比分别为$\alpha$和$1-\alpha$。

% 探针分布的设计

% 同时，我们不想引入额外的参数化模型，因为这会导致计算密度函数的时间复杂度随着行为分布的数量线性增加。





% 具体来说，这个探针是由当前的策略 LLM 和一个 prompt pool 所组成。

% 在 REX-RAG 中，我们使用一个虚拟的探针分布$\pi_\mu$增加策略的搜索空间大小，同时减少训练过程中死胡同的出现。具体来说，这个探针分布是




% \paragraph{Training objective}

% During training, for each query \(q\), the policy generates trajectories \(o = \{ o_1, o_2 \dots o_G\}\).




% 具体来说，


% 而 RLVR 方法利用现有的 RLHF 方法和目标，而将奖励模型替换为一个验证函数。





