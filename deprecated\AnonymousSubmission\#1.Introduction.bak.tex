\section{Introduction}

Recent advances have shown that reinforcement learning (RL) offers a promising avenue for training large language models (LLMs) to perform complex reasoning tasks. By integrating multi-step reasoning with retrieval-augmented generation (RAG), RL-trained LLMs can dynamically leverage external knowledge sources—essentially allowing them to “think while searching.”

% This paradigm holds particular promise for multi-hop question answering (QA), where a model must iteratively gather and synthesize evidence across multiple queries to arrive at a well-founded conclusion.

Despite this potential, we observe a critical challenge that substantially hinders policy optimization in such settings. During RL training, LLMs frequently become trapped in what we term dead ends: situations where, after multiple rollouts, the model consistently fails to arrive at the correct final answer. This often stems from premature or overconfident conclusions drawn despite insufficient supporting information, effectively terminating exploration along fruitful reasoning trajectories. In our experiments with the Qwen2.5-3B-Base model, we found that approximately ?\% of training instances resulted in .


% 这一段需要把self-reflection 为什么fail加进去
Addressing this challenge requires mechanisms that can proactively explore alternative reasoning paths when initial trajectories prove unproductive. A straightforward solution is \textit{self-reflection}\cite{}, which attempts to revise failed reasoning chains to generate alternative ones. However, these revised trajectories are often merely slight perturbations of the original paths, offering limited novelty and insufficient deviation to meaningfully explore alternative solutions, and fail to drive the reasoning process out of local optima. On the other hand, more aggressively enforcing exploration, such as introducing new agents, can introduce substantial distributional shifts\cite{}, leading to biased policy updates and destabilizing training. This tension underscores the need for principled strategies that can foster sufficiently diverse and informative exploration while ensuring stable and unbiased policy optimization.


% Yet naively modifying the model’s behavior to seek out new paths introduces significant distributional shifts, potentially biasing the policy updates and undermining stable learning. This tension highlights the need for principled approaches that not only encourage broader exploration but also maintain the integrity of policy optimization.


% To mitigate this, we introduce a simple yet effective strategy based on revision prompts. Specifically, we maintain a diverse collection of GPT-generated prompts designed to encourage alternative reasoning when the model becomes stuck. Upon detecting a dead end, we append a randomly selected revision prompt from this pool to the failed trajectory, thereby nudging the model to explore new paths that might lead to the correct answer. This approach generalizes beyond conventional self-reflection methods by leveraging a richer set of interventions to drive broader exploration.

% However, naively incorporating these externally induced revisions poses a significant challenge: the resulting trajectories deviate from the original model policy, introducing distributional shifts that can bias gradient updates. To address this, we propose Revision-Regularized Policy Optimization (RRPO), a principled framework comprising three key components. First, we sample initial trajectories according to the model’s current policy. Second, we estimate the likelihood distribution over tokens conditioned on various revision prompts, effectively modeling the influence of our revision pool. Finally, we correct for the induced bias by applying multiple importance sampling, which adjusts the policy gradients to account for the altered sampling distribution. This enables us to integrate the benefits of revision-driven exploration while maintaining theoretically sound and unbiased policy optimization.


% 这一段需要概括方法 To address this challenge, we propose a novel ...
To tackle this challenge, we propose REX-RAG, a novel framework that systematically explores alternative reasoning paths while maintaining rigorous policy learning through principled distributional corrections. Our approach introduces a virtual probe distribution $\pi_\mu$ that works alongside the standard policy $\pi_\theta$ to guide exploration away from dead ends through mixed sampling strategies.

% 这一段需要稍微具体讲述方法思想
% model-prompt alternation mechanism有点难以理解，这一段还需修改
The key innovation of REX-RAG lies in its lightweight model-prompt alternation mechanism.
Rather than training additional parameterized models or relying on simple self-reflection that produces limited variations, our framework employs a curated collection of chain-of-thought prompts to inject diverse reasoning directions when trajectories fail. When the policy encounters a dead end—indicated by an incorrect answer—we strategically insert concise reasoning hints from a pre-constructed prompt pool and resume generation, effectively steering the model toward unexplored solution paths.

Crucially, to prevent the distributional shifts inherent in such interventions from destabilizing training, REX-RAG incorporates bias correction mechanisms based on importance sampling theory. This ensures that policy gradients remain unbiased despite the mixed sampling from both the original policy and the probe distribution.

Experiments shows that REX-RAG ...

% This design addresses a fundamental limitation of existing self-reflection approaches: while traditional methods generate minor perturbations of failed reasoning chains, our probe distribution introduces substantively different reasoning trajectories that can escape local optima. Crucially, to prevent the distributional shifts inherent in such interventions from destabilizing training, REX-RAG incorporates bias correction mechanisms based on importance sampling theory. This ensures that policy gradients remain unbiased despite the mixed sampling from both the original policy and the probe distribution.

% Our framework is implemented using Group Relative Policy Optimization (GRPO), which naturally accommodates the mixed trajectory sampling while maintaining computational efficiency. By training on both policy-generated and probe-guided trajectories, the model learns not only to condition on external hints but also to internalize the underlying reasoning patterns, enabling autonomous discovery of high-value solution paths in future iterations.
